import { initializeFirebase, db } from './firebase';
import { Blog, BlogStatus, CreateBlogData, UpdateBlogData } from '@/types';
import { uploadImage, deleteImage } from './storageService';

/**
 * Gets all blogs from Firestore
 * @returns Promise<Blog[]> - Array of blogs sorted by creation date (newest first)
 */
export const getAllBlogs = async (): Promise<Blog[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, getDocs, orderBy } = await import('firebase/firestore');

    console.log('Fetching all blogs from Firestore');

    // Create a reference to the blogs collection
    const blogsRef = collection(db, 'blogs');

    // Create a query to get all blogs ordered by creation date (newest first)
    const blogsQuery = query(blogsRef, orderBy('createdAt', 'desc'));

    // Execute the query
    const querySnapshot = await getDocs(blogsQuery);

    // Map the query results to Blog objects
    const blogs: Blog[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Convert Firestore timestamps to Date objects
      const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();
      const updatedAt = data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date();

      // Create a Blog object from the document data
      const blog: Blog = {
        id: doc.id,
        title: data.title || '',
        content: data.content || '',
        tags: Array.isArray(data.tags) ? data.tags : [],
        published: data.published || false,
        status: data.published ? BlogStatus.Published : BlogStatus.Draft,
        createdAt: createdAt,
        updatedAt: updatedAt,
        authorId: data.authorId || '',
        authorName: data.authorName || '',
        authorEmail: data.authorEmail || '',
        slug: data.slug,
        excerpt: data.excerpt,
        readTime: data.readTime,
        views: data.views || 0,
        coverImageUrl: data.coverImageUrl
      };

      blogs.push(blog);
    });

    console.log(`Found ${blogs.length} blogs in Firestore`);
    return blogs;
  } catch (error) {
    console.error('Error getting blogs from Firestore:', error);
    throw error;
  }
};

/**
 * Gets a blog by ID from Firestore
 * @param blogId - The ID of the blog to fetch
 * @returns Promise<Blog | null> - The blog or null if not found
 */
export const getBlog = async (blogId: string): Promise<Blog | null> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc } = await import('firebase/firestore');

    console.log(`Fetching blog with ID: ${blogId} from Firestore`);

    // Get the blog document from Firestore
    const blogRef = doc(db, 'blogs', blogId);
    const blogSnapshot = await getDoc(blogRef);

    if (!blogSnapshot.exists()) {
      console.log(`No blog found with ID: ${blogId} in Firestore`);
      return null;
    }

    // Get the blog data
    const data = blogSnapshot.data();

    // Convert Firestore timestamps to Date objects
    const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();
    const updatedAt = data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date();

    // Create a Blog object from the document data
    const blog: Blog = {
      id: blogSnapshot.id,
      title: data.title || '',
      content: data.content || '',
      tags: Array.isArray(data.tags) ? data.tags : [],
      published: data.published || false,
      status: data.published ? BlogStatus.Published : BlogStatus.Draft,
      createdAt: createdAt,
      updatedAt: updatedAt,
      authorId: data.authorId || '',
      authorName: data.authorName || '',
      authorEmail: data.authorEmail || '',
      slug: data.slug,
      excerpt: data.excerpt,
      readTime: data.readTime,
      views: data.views || 0,
      coverImageUrl: data.coverImageUrl
    };

    console.log(`Found blog in Firestore: ${blog.title}`);
    return blog;
  } catch (error) {
    console.error('Error getting blog from Firestore:', error);
    throw error;
  }
};

/**
 * Creates a new blog in Firestore
 * @param blogData - The blog data to create
 * @returns Promise<string> - The ID of the created blog
 */
export const createBlog = async (blogData: CreateBlogData): Promise<string> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Creating new blog in Firestore:', blogData.title);

    // Generate slug from title
    const slug = generateSlug(blogData.title);

    // Estimate reading time (average 200 words per minute)
    const readTime = estimateReadingTime(blogData.content);

    // Create a reference to the blogs collection
    const blogsRef = collection(db, 'blogs');

    // Prepare the blog document data
    const blogDocument = {
      title: blogData.title,
      content: blogData.content,
      tags: blogData.tags,
      published: blogData.published,
      authorId: blogData.authorId,
      authorName: blogData.authorName,
      authorEmail: blogData.authorEmail,
      slug: slug,
      excerpt: blogData.excerpt || generateExcerpt(blogData.content),
      readTime: readTime,
      views: 0,
      coverImageUrl: blogData.coverImageUrl || null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Add the blog to Firestore
    const docRef = await addDoc(blogsRef, blogDocument);

    console.log(`Blog created successfully with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error('Error creating blog:', error);
    throw error;
  }
};

/**
 * Updates a blog in Firestore
 * @param blogId - The ID of the blog to update
 * @param blogData - The blog data to update
 * @returns Promise<void>
 */
export const updateBlog = async (blogId: string, blogData: UpdateBlogData): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Updating blog with ID: ${blogId}`);

    const blogRef = doc(db, 'blogs', blogId);

    // Check if the blog exists
    const blogSnapshot = await getDoc(blogRef);
    if (!blogSnapshot.exists()) {
      throw new Error(`Blog with ID ${blogId} not found`);
    }

    // Prepare update data
    const updateData: Record<string, any> = {
      updatedAt: serverTimestamp()
    };

    // Add fields that are being updated
    if (blogData.title !== undefined) {
      updateData.title = blogData.title;
      updateData.slug = generateSlug(blogData.title);
    }

    if (blogData.content !== undefined) {
      updateData.content = blogData.content;
      updateData.readTime = estimateReadingTime(blogData.content);
      if (!blogData.excerpt) {
        updateData.excerpt = generateExcerpt(blogData.content);
      }
    }

    if (blogData.tags !== undefined) {
      updateData.tags = blogData.tags;
    }

    if (blogData.published !== undefined) {
      updateData.published = blogData.published;
    }

    if (blogData.excerpt !== undefined) {
      updateData.excerpt = blogData.excerpt;
    }

    if (blogData.coverImageUrl !== undefined) {
      updateData.coverImageUrl = blogData.coverImageUrl;
    }

    // Perform the update
    await updateDoc(blogRef, updateData);

    console.log(`Blog ${blogId} updated successfully`);
  } catch (error) {
    console.error('Error updating blog:', error);
    throw error;
  }
};

/**
 * Deletes a blog from Firestore
 * @param blogId - The ID of the blog to delete
 * @returns Promise<void>
 */
export const deleteBlog = async (blogId: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, deleteDoc } = await import('firebase/firestore');

    console.log(`Deleting blog with ID: ${blogId}`);

    const blogRef = doc(db, 'blogs', blogId);

    // Check if the blog exists
    const blogSnapshot = await getDoc(blogRef);
    if (!blogSnapshot.exists()) {
      throw new Error(`Blog with ID ${blogId} not found`);
    }

    // Delete the blog
    await deleteDoc(blogRef);

    console.log(`Blog ${blogId} deleted successfully`);
  } catch (error) {
    console.error('Error deleting blog:', error);
    throw error;
  }
};

/**
 * Toggles the published status of a blog
 * @param blogId - The ID of the blog to toggle
 * @returns Promise<boolean> - The new published status
 */
export const togglePublishStatus = async (blogId: string): Promise<boolean> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Toggling publish status for blog with ID: ${blogId}`);

    const blogRef = doc(db, 'blogs', blogId);

    // Get current blog data
    const blogSnapshot = await getDoc(blogRef);
    if (!blogSnapshot.exists()) {
      throw new Error(`Blog with ID ${blogId} not found`);
    }

    const currentData = blogSnapshot.data();
    const newPublishedStatus = !currentData.published;

    // Update the published status
    await updateDoc(blogRef, {
      published: newPublishedStatus,
      updatedAt: serverTimestamp()
    });

    console.log(`Blog ${blogId} publish status toggled to: ${newPublishedStatus}`);
    return newPublishedStatus;
  } catch (error) {
    console.error('Error toggling blog publish status:', error);
    throw error;
  }
};

// Helper functions

/**
 * Generates a URL-friendly slug from a title
 * @param title - The title to convert to a slug
 * @returns string - The generated slug
 */
const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Generates an excerpt from content
 * @param content - The content to generate excerpt from
 * @returns string - The generated excerpt
 */
const generateExcerpt = (content: string): string => {
  // Remove HTML tags and get first 150 characters
  const plainText = content.replace(/<[^>]*>/g, '');
  return plainText.length > 150 ? plainText.substring(0, 150) + '...' : plainText;
};

/**
 * Estimates reading time based on content length
 * @param content - The content to analyze
 * @returns number - Estimated reading time in minutes
 */
const estimateReadingTime = (content: string): number => {
  const wordsPerMinute = 200;
  const plainText = content.replace(/<[^>]*>/g, '');
  const wordCount = plainText.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
};

/**
 * Uploads a blog cover image to Firebase Storage
 * @param imageFile - The image file to upload
 * @param blogId - The blog ID for path generation (optional, generates unique path if not provided)
 * @param onProgress - Optional callback for tracking upload progress
 * @returns Promise<string> - The download URL of the uploaded image
 */
export const uploadBlogCoverImage = async (
  imageFile: File,
  blogId?: string,
  onProgress?: (progress: number) => void
): Promise<string> => {
  try {
    console.log('Uploading blog cover image:', imageFile.name);

    // Generate a unique path for the blog cover image
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 6);
    const fileExtension = 'webp'; // Use WebP for optimization

    const path = blogId
      ? `blog-covers/${blogId}/${timestamp}-${randomString}.${fileExtension}`
      : `blog-covers/temp/${timestamp}-${randomString}.${fileExtension}`;

    // Upload the image with optimization
    const downloadURL = await uploadImage(imageFile, path, onProgress, {
      maxWidth: 1200,
      maxHeight: 800,
      quality: 0.85
    });

    console.log('Blog cover image uploaded successfully:', downloadURL);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading blog cover image:', error);
    throw error;
  }
};

/**
 * Deletes a blog cover image from Firebase Storage
 * @param imageUrl - The URL of the image to delete
 * @returns Promise<boolean> - True if deletion was successful
 */
export const deleteBlogCoverImage = async (imageUrl: string): Promise<boolean> => {
  try {
    console.log('Deleting blog cover image:', imageUrl);
    const success = await deleteImage(imageUrl);
    console.log('Blog cover image deletion result:', success);
    return success;
  } catch (error) {
    console.error('Error deleting blog cover image:', error);
    return false;
  }
};
