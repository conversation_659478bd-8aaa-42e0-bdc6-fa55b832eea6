import React, { useEffect, useState, useCallback, useRef } from 'react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Spinner } from '@/components/ui/spinner';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Blog, BlogFormData, UpdateBlogData } from '@/types';
import { uploadBlogCoverImage, deleteBlogCoverImage, autosaveBlogDraft } from '@/lib/blogService';
import { toast } from 'sonner';
import { ImageIcon, X, Upload, Eye, EyeOff, Save, Clock } from 'lucide-react';

// Validation schema for blog form
const blogSchema = z.object({
  title: z.string().min(1, { message: 'Title is required' }).max(200, { message: 'Title must be less than 200 characters' }),
  content: z.string().min(1, { message: 'Content is required' }).min(10, { message: 'Content must be at least 10 characters' }),
  tags: z.array(z.string()).min(0).max(10, { message: 'Maximum 10 tags allowed' }),
  published: z.boolean(),
  excerpt: z.string().max(300, { message: 'Excerpt must be less than 300 characters' }).optional(),
  coverImageUrl: z.string().optional(),
});

// Rich text editor configuration
const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['blockquote', 'code-block'],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'blockquote', 'code-block', 'link'
];

type BlogFormValues = z.infer<typeof blogSchema>;

interface BlogEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: BlogFormData) => Promise<void>;
  blog?: Blog | null;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

const BlogEditor: React.FC<BlogEditorProps> = ({
  isOpen,
  onClose,
  onSave,
  blog,
  isLoading = false,
  mode
}) => {
  // Image upload state
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string>('');
  const [imageUploadProgress, setImageUploadProgress] = useState<number>(0);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Preview and autosave state
  const [showPreview, setShowPreview] = useState(false);
  const [lastAutosave, setLastAutosave] = useState<Date | null>(null);
  const [isAutosaving, setIsAutosaving] = useState(false);
  const [autosaveError, setAutosaveError] = useState<string | null>(null);

  // Refs for debouncing
  const autosaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const previewUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const form = useForm<BlogFormValues>({
    resolver: zodResolver(blogSchema),
    defaultValues: {
      title: '',
      content: '',
      tags: [],
      published: false,
      excerpt: '',
      coverImageUrl: '',
    },
  });

  // Autosave function
  const performAutosave = useCallback(async (formData: BlogFormValues) => {
    // Only autosave for existing blogs (edit mode) and when not published
    if (mode !== 'edit' || !blog?.id || formData.published) {
      return;
    }

    try {
      setIsAutosaving(true);
      setAutosaveError(null);

      const updateData: UpdateBlogData = {
        title: formData.title,
        content: formData.content,
        tags: formData.tags,
        excerpt: formData.excerpt,
        coverImageUrl: formData.coverImageUrl,
        published: false, // Keep as draft during autosave
      };

      await autosaveBlogDraft(blog.id, updateData);
      setLastAutosave(new Date());
    } catch (error) {
      console.error('Autosave failed:', error);
      setAutosaveError('Failed to autosave');
    } finally {
      setIsAutosaving(false);
    }
  }, [mode, blog?.id]);

  // Debounced autosave effect
  useEffect(() => {
    if (!isOpen) return;

    const subscription = form.watch((formData) => {
      // Clear existing timeout
      if (autosaveTimeoutRef.current) {
        clearTimeout(autosaveTimeoutRef.current);
      }

      // Only autosave if we have meaningful content and it's a draft
      if (formData.title || formData.content) {
        autosaveTimeoutRef.current = setTimeout(() => {
          performAutosave(formData as BlogFormValues);
        }, 10000); // 10 seconds
      }
    });

    return () => {
      subscription.unsubscribe();
      if (autosaveTimeoutRef.current) {
        clearTimeout(autosaveTimeoutRef.current);
      }
    };
  }, [isOpen, form, performAutosave]);

  // Reset form when blog changes or dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && blog) {
        form.reset({
          title: blog.title,
          content: blog.content,
          tags: blog.tags,
          published: blog.published,
          excerpt: blog.excerpt || '',
          coverImageUrl: blog.coverImageUrl || '',
        });
        // Set cover image preview if editing
        if (blog.coverImageUrl) {
          setCoverImagePreview(blog.coverImageUrl);
        }
      } else if (mode === 'create') {
        form.reset({
          title: '',
          content: '',
          tags: [],
          published: false,
          excerpt: '',
          coverImageUrl: '',
        });
        setCoverImagePreview('');
        setCoverImage(null);
      }

      // Reset autosave state
      setLastAutosave(null);
      setIsAutosaving(false);
      setAutosaveError(null);
      setShowPreview(false);
    }
  }, [isOpen, blog, mode, form]);

  // Handle cover image upload
  const handleImageUpload = useCallback(async (file: File) => {
    try {
      setIsUploadingImage(true);
      setImageUploadProgress(0);

      // Upload the image
      const imageUrl = await uploadBlogCoverImage(file, undefined, (progress) => {
        setImageUploadProgress(progress);
      });

      // Update form and preview
      form.setValue('coverImageUrl', imageUrl);
      setCoverImagePreview(imageUrl);
      setCoverImage(file);

      toast.success('Cover image uploaded successfully');
    } catch (error) {
      console.error('Error uploading cover image:', error);
      toast.error('Failed to upload cover image');
    } finally {
      setIsUploadingImage(false);
      setImageUploadProgress(0);
    }
  }, [form]);

  // Handle image file selection
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size must be less than 5MB');
        return;
      }

      handleImageUpload(file);
    }
  };

  // Remove cover image
  const handleRemoveImage = async () => {
    const currentImageUrl = form.getValues('coverImageUrl');

    // If there's an existing image URL and we're editing, attempt to delete it
    if (currentImageUrl && mode === 'edit') {
      try {
        await deleteBlogCoverImage(currentImageUrl);
      } catch (error) {
        console.warn('Failed to delete previous cover image:', error);
      }
    }

    form.setValue('coverImageUrl', '');
    setCoverImagePreview('');
    setCoverImage(null);
  };

  const handleSubmit = async (values: BlogFormValues) => {
    try {
      const blogData: BlogFormData = {
        title: values.title,
        content: values.content,
        tags: values.tags,
        published: values.published,
        excerpt: values.excerpt,
        coverImageUrl: values.coverImageUrl,
      };

      await onSave(blogData);
      onClose();
    } catch (error) {
      console.error('Error saving blog:', error);
    }
  };

  const handleTagsChange = (value: string) => {
    // Convert comma-separated string to array of tags
    const tagsArray = value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
    
    form.setValue('tags', tagsArray);
  };

  const handleClose = () => {
    // Clear timeouts
    if (autosaveTimeoutRef.current) {
      clearTimeout(autosaveTimeoutRef.current);
    }
    if (previewUpdateTimeoutRef.current) {
      clearTimeout(previewUpdateTimeoutRef.current);
    }

    form.reset();
    onClose();
  };

  // Format autosave timestamp
  const formatAutosaveTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Blog preview component
  const BlogPreview: React.FC<{ formData: BlogFormValues }> = ({ formData }) => {
    return (
      <div className="bg-white border rounded-lg p-6 h-full overflow-y-auto">
        <div className="prose prose-sm max-w-none">
          {/* Cover Image */}
          {formData.coverImageUrl && (
            <div className="mb-6">
              <img
                src={formData.coverImageUrl}
                alt="Cover"
                className="w-full h-48 object-cover rounded-lg"
              />
            </div>
          )}

          {/* Title */}
          <h1 className="text-2xl font-bold text-navy-800 mb-2">
            {formData.title || 'Untitled Blog Post'}
          </h1>

          {/* Excerpt */}
          {formData.excerpt && (
            <p className="text-gray-600 italic mb-4 border-l-4 border-burgundy-200 pl-4">
              {formData.excerpt}
            </p>
          )}

          {/* Tags */}
          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-6">
              {formData.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="bg-beige-100 text-navy-700">
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Content */}
          <div
            className="prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{
              __html: formData.content || '<p class="text-gray-400">Start writing your blog post content...</p>'
            }}
          />

          {/* Publication Status */}
          <div className="mt-8 pt-4 border-t border-gray-200">
            <Badge variant={formData.published ? "default" : "secondary"}>
              {formData.published ? 'Published' : 'Draft'}
            </Badge>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={`max-h-[90vh] overflow-hidden ${showPreview ? 'max-w-7xl' : 'max-w-4xl'}`}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-navy-800">
                {mode === 'create' ? 'Create New Blog Post' : 'Edit Blog Post'}
              </DialogTitle>
              <DialogDescription>
                {mode === 'create'
                  ? 'Create a new blog post for your readers. You can save as draft or publish immediately.'
                  : 'Edit your blog post. Changes will be saved when you click Save.'
                }
              </DialogDescription>
            </div>

            <div className="flex items-center gap-2">
              {/* Autosave Status */}
              {mode === 'edit' && !form.watch('published') && (
                <div className="flex items-center gap-2 text-sm">
                  {isAutosaving ? (
                    <div className="flex items-center gap-1 text-blue-600">
                      <Spinner size="sm" />
                      <span>Saving...</span>
                    </div>
                  ) : lastAutosave ? (
                    <div className="flex items-center gap-1 text-green-600">
                      <Save className="h-3 w-3" />
                      <span>Saved at {formatAutosaveTime(lastAutosave)}</span>
                    </div>
                  ) : autosaveError ? (
                    <div className="flex items-center gap-1 text-red-600">
                      <X className="h-3 w-3" />
                      <span>{autosaveError}</span>
                    </div>
                  ) : null}
                </div>
              )}

              {/* Preview Toggle */}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
                className="flex items-center gap-1"
              >
                {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className={`flex gap-6 ${showPreview ? 'h-[calc(90vh-200px)]' : ''}`}>
          {/* Editor Section */}
          <div className={`${showPreview ? 'w-1/2' : 'w-full'} ${showPreview ? 'overflow-y-auto' : ''}`}>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter blog post title..."
                      {...field}
                      disabled={isLoading}
                      className="text-lg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cover Image */}
            <FormField
              control={form.control}
              name="coverImageUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cover Image</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      {coverImagePreview ? (
                        <div className="relative">
                          <img
                            src={coverImagePreview}
                            alt="Cover preview"
                            className="w-full h-48 object-cover rounded-lg border"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute top-2 right-2"
                            onClick={handleRemoveImage}
                            disabled={isLoading || isUploadingImage}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <div className="mt-4">
                            <label htmlFor="cover-image-upload" className="cursor-pointer">
                              <span className="mt-2 block text-sm font-medium text-gray-900">
                                Upload cover image
                              </span>
                              <span className="mt-1 block text-sm text-gray-500">
                                PNG, JPG, WebP up to 5MB
                              </span>
                            </label>
                            <input
                              id="cover-image-upload"
                              type="file"
                              className="hidden"
                              accept="image/*"
                              onChange={handleImageChange}
                              disabled={isLoading || isUploadingImage}
                            />
                          </div>
                        </div>
                      )}

                      {isUploadingImage && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Uploading image...</span>
                            <span>{imageUploadProgress}%</span>
                          </div>
                          <Progress value={imageUploadProgress} className="w-full" />
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormDescription>
                    Upload a cover image for your blog post. This will be displayed as the main image.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Excerpt */}
            <FormField
              control={form.control}
              name="excerpt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Excerpt</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of the blog post (optional)..."
                      {...field}
                      disabled={isLoading}
                      rows={2}
                    />
                  </FormControl>
                  <FormDescription>
                    A short summary that will be displayed in blog previews. If left empty, it will be auto-generated from content.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Content - Rich Text Editor */}
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content *</FormLabel>
                  <FormControl>
                    <div className="min-h-[300px]">
                      <ReactQuill
                        theme="snow"
                        value={field.value}
                        onChange={field.onChange}
                        modules={quillModules}
                        formats={quillFormats}
                        placeholder="Write your blog post content here..."
                        className="bg-white"
                        style={{ height: '250px', marginBottom: '50px' }}
                        readOnly={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Write your blog post content using the rich text editor. You can format text, add headers, lists, and links.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags */}
            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter tags separated by commas (e.g., books, reading, reviews)"
                      value={field.value.join(', ')}
                      onChange={(e) => handleTagsChange(e.target.value)}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Add tags to help categorize your blog post. Separate multiple tags with commas.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Published Status */}
            <FormField
              control={form.control}
              name="published"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Publish immediately</FormLabel>
                    <FormDescription>
                      Toggle this to publish the blog post immediately. If disabled, it will be saved as a draft.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

                <DialogFooter className="gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="bg-burgundy-600 hover:bg-burgundy-700"
                  >
                    {isLoading ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        {mode === 'create' ? 'Creating...' : 'Saving...'}
                      </>
                    ) : (
                      <>
                        {form.watch('published')
                          ? (mode === 'create' ? 'Create & Publish' : 'Save & Publish')
                          : (mode === 'create' ? 'Save as Draft' : 'Save Changes')
                        }
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </div>

          {/* Preview Section */}
          {showPreview && (
            <div className="w-1/2 border-l border-gray-200 pl-6">
              <div className="sticky top-0 bg-white pb-4 mb-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-navy-800 flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Live Preview
                </h3>
                <p className="text-sm text-gray-600">See how your blog post will appear to readers</p>
              </div>
              <BlogPreview formData={form.watch()} />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BlogEditor;
