import React, { useEffect, useState, useCallback } from 'react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Spinner } from '@/components/ui/spinner';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Blog, BlogFormData } from '@/types';
import { uploadBlogCoverImage, deleteBlogCoverImage } from '@/lib/blogService';
import { toast } from 'sonner';
import { ImageIcon, X, Upload } from 'lucide-react';

// Validation schema for blog form
const blogSchema = z.object({
  title: z.string().min(1, { message: 'Title is required' }).max(200, { message: 'Title must be less than 200 characters' }),
  content: z.string().min(1, { message: 'Content is required' }).min(10, { message: 'Content must be at least 10 characters' }),
  tags: z.array(z.string()).min(0).max(10, { message: 'Maximum 10 tags allowed' }),
  published: z.boolean(),
  excerpt: z.string().max(300, { message: 'Excerpt must be less than 300 characters' }).optional(),
  coverImageUrl: z.string().optional(),
});

// Rich text editor configuration
const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['blockquote', 'code-block'],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'blockquote', 'code-block', 'link'
];

type BlogFormValues = z.infer<typeof blogSchema>;

interface BlogEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: BlogFormData) => Promise<void>;
  blog?: Blog | null;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

const BlogEditor: React.FC<BlogEditorProps> = ({
  isOpen,
  onClose,
  onSave,
  blog,
  isLoading = false,
  mode
}) => {
  // Image upload state
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string>('');
  const [imageUploadProgress, setImageUploadProgress] = useState<number>(0);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const form = useForm<BlogFormValues>({
    resolver: zodResolver(blogSchema),
    defaultValues: {
      title: '',
      content: '',
      tags: [],
      published: false,
      excerpt: '',
      coverImageUrl: '',
    },
  });

  // Reset form when blog changes or dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && blog) {
        form.reset({
          title: blog.title,
          content: blog.content,
          tags: blog.tags,
          published: blog.published,
          excerpt: blog.excerpt || '',
          coverImageUrl: blog.coverImageUrl || '',
        });
        // Set cover image preview if editing
        if (blog.coverImageUrl) {
          setCoverImagePreview(blog.coverImageUrl);
        }
      } else if (mode === 'create') {
        form.reset({
          title: '',
          content: '',
          tags: [],
          published: false,
          excerpt: '',
          coverImageUrl: '',
        });
        setCoverImagePreview('');
        setCoverImage(null);
      }
    }
  }, [isOpen, blog, mode, form]);

  // Handle cover image upload
  const handleImageUpload = useCallback(async (file: File) => {
    try {
      setIsUploadingImage(true);
      setImageUploadProgress(0);

      // Upload the image
      const imageUrl = await uploadBlogCoverImage(file, undefined, (progress) => {
        setImageUploadProgress(progress);
      });

      // Update form and preview
      form.setValue('coverImageUrl', imageUrl);
      setCoverImagePreview(imageUrl);
      setCoverImage(file);

      toast.success('Cover image uploaded successfully');
    } catch (error) {
      console.error('Error uploading cover image:', error);
      toast.error('Failed to upload cover image');
    } finally {
      setIsUploadingImage(false);
      setImageUploadProgress(0);
    }
  }, [form]);

  // Handle image file selection
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size must be less than 5MB');
        return;
      }

      handleImageUpload(file);
    }
  };

  // Remove cover image
  const handleRemoveImage = async () => {
    const currentImageUrl = form.getValues('coverImageUrl');

    // If there's an existing image URL and we're editing, attempt to delete it
    if (currentImageUrl && mode === 'edit') {
      try {
        await deleteBlogCoverImage(currentImageUrl);
      } catch (error) {
        console.warn('Failed to delete previous cover image:', error);
      }
    }

    form.setValue('coverImageUrl', '');
    setCoverImagePreview('');
    setCoverImage(null);
  };

  const handleSubmit = async (values: BlogFormValues) => {
    try {
      const blogData: BlogFormData = {
        title: values.title,
        content: values.content,
        tags: values.tags,
        published: values.published,
        excerpt: values.excerpt,
        coverImageUrl: values.coverImageUrl,
      };

      await onSave(blogData);
      onClose();
    } catch (error) {
      console.error('Error saving blog:', error);
    }
  };

  const handleTagsChange = (value: string) => {
    // Convert comma-separated string to array of tags
    const tagsArray = value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
    
    form.setValue('tags', tagsArray);
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-navy-800">
            {mode === 'create' ? 'Create New Blog Post' : 'Edit Blog Post'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Create a new blog post for your readers. You can save as draft or publish immediately.'
              : 'Edit your blog post. Changes will be saved when you click Save.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter blog post title..."
                      {...field}
                      disabled={isLoading}
                      className="text-lg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cover Image */}
            <FormField
              control={form.control}
              name="coverImageUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cover Image</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      {coverImagePreview ? (
                        <div className="relative">
                          <img
                            src={coverImagePreview}
                            alt="Cover preview"
                            className="w-full h-48 object-cover rounded-lg border"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute top-2 right-2"
                            onClick={handleRemoveImage}
                            disabled={isLoading || isUploadingImage}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <div className="mt-4">
                            <label htmlFor="cover-image-upload" className="cursor-pointer">
                              <span className="mt-2 block text-sm font-medium text-gray-900">
                                Upload cover image
                              </span>
                              <span className="mt-1 block text-sm text-gray-500">
                                PNG, JPG, WebP up to 5MB
                              </span>
                            </label>
                            <input
                              id="cover-image-upload"
                              type="file"
                              className="hidden"
                              accept="image/*"
                              onChange={handleImageChange}
                              disabled={isLoading || isUploadingImage}
                            />
                          </div>
                        </div>
                      )}

                      {isUploadingImage && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Uploading image...</span>
                            <span>{imageUploadProgress}%</span>
                          </div>
                          <Progress value={imageUploadProgress} className="w-full" />
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormDescription>
                    Upload a cover image for your blog post. This will be displayed as the main image.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Excerpt */}
            <FormField
              control={form.control}
              name="excerpt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Excerpt</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of the blog post (optional)..."
                      {...field}
                      disabled={isLoading}
                      rows={2}
                    />
                  </FormControl>
                  <FormDescription>
                    A short summary that will be displayed in blog previews. If left empty, it will be auto-generated from content.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Content - Rich Text Editor */}
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content *</FormLabel>
                  <FormControl>
                    <div className="min-h-[300px]">
                      <ReactQuill
                        theme="snow"
                        value={field.value}
                        onChange={field.onChange}
                        modules={quillModules}
                        formats={quillFormats}
                        placeholder="Write your blog post content here..."
                        className="bg-white"
                        style={{ height: '250px', marginBottom: '50px' }}
                        readOnly={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Write your blog post content using the rich text editor. You can format text, add headers, lists, and links.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags */}
            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter tags separated by commas (e.g., books, reading, reviews)"
                      value={field.value.join(', ')}
                      onChange={(e) => handleTagsChange(e.target.value)}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Add tags to help categorize your blog post. Separate multiple tags with commas.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Published Status */}
            <FormField
              control={form.control}
              name="published"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Publish immediately</FormLabel>
                    <FormDescription>
                      Toggle this to publish the blog post immediately. If disabled, it will be saved as a draft.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-burgundy-600 hover:bg-burgundy-700"
              >
                {isLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    {mode === 'create' ? 'Creating...' : 'Saving...'}
                  </>
                ) : (
                  <>
                    {form.watch('published') 
                      ? (mode === 'create' ? 'Create & Publish' : 'Save & Publish')
                      : (mode === 'create' ? 'Save as Draft' : 'Save Changes')
                    }
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default BlogEditor;
